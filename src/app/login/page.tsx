"use client";

import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
  Stack
} from '@mui/material';
import { Google as GoogleIcon } from '@mui/icons-material';
import MaterialThemeProvider from '@/components/ThemeProvider';



export default function LoginPage() {
  const router = useRouter();

  const handleGoogleSignIn = () => {
    // TODO: Implement Google OAuth sign-in
    console.log('Google Sign-In clicked');
    // For now, simulate successful login and redirect to dashboard
    router.push('/dashboard');
  };
  return (
    <MaterialThemeProvider>
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          padding: 2,
        }}
      >
        <Container maxWidth="sm">
          <Paper
            elevation={8}
            sx={{
              padding: { xs: 3, sm: 4, md: 6 },
              borderRadius: 3,
              textAlign: 'center',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
            }}
          >
            <Stack spacing={2} alignItems="center">
              {/* Logo */}
              <Box
                sx={{
                  width: { xs: 120, sm: 150, md: 180 },
                  height: { xs: 120, sm: 150, md: 180 },
                  position: 'relative',
                }}
              >
                <Image
                  src="/equilibrium_logo.svg"
                  alt="Equilibrium Logo"
                  fill
                  style={{ objectFit: 'contain' }}
                  priority
                />
              </Box>

              {/* Project Name */}
              <Typography
                variant="h1"
                component="h1"
                sx={{
                  color: 'primary.main',
                  fontWeight: 600,
                  fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                }}
              >
                Equilibrium
              </Typography>

              {/* Slogan */}
              <Typography
                variant="h2"
                component="h2"
                sx={{
                  color: 'text.secondary',
                  fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.5rem' },
                  fontWeight: 400,
                }}
              >
                For your work-life balance
              </Typography>

              {/* Sign in Button */}
              <Box sx={{ mt: '48px !important' }}>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<GoogleIcon />}
                  onClick={handleGoogleSignIn}
                  sx={{
                    minWidth: { xs: '280px', sm: '320px' },
                    height: '56px',
                    fontSize: '1.1rem',
                    fontWeight: 500,
                    background: '#4285f4',
                    color: 'white',
                    '&:hover': {
                      background: '#3367d6',
                    },
                    '&:active': {
                      transform: 'scale(0.98)',
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                >
                  Sign in with Google
                </Button>
              </Box>

              {/* Additional info */}
              <Typography
                variant="body2"
                sx={{
                  color: 'text.secondary',
                  fontSize: '0.875rem',
                  marginTop: 2,
                  maxWidth: '300px',
                }}
              >
                Secure authentication powered by Google OAuth
              </Typography>
            </Stack>
          </Paper>
        </Container>
      </Box>
    </MaterialThemeProvider>
  );
}
