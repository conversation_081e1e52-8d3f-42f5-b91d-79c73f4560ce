"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Typo<PERSON>,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Paper
} from '@mui/material';
import {
  Add as AddIcon,
  Settings as SettingsIcon,
  AccountCircle,
  Logout as LogoutIcon
} from '@mui/icons-material';
import MaterialThemeProvider from '@/components/ThemeProvider';

export default function Dashboard() {
  const router = useRouter();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleClose();
    // TODO: Implement actual logout logic
    console.log('Logout clicked');
    router.push('/login');
  };

  const handleManageShiftTypes = () => {
    router.push('/manage-shift-types');
  };

  const handleCreateShift = () => {
    // TODO: This will be implemented later
    console.log('Create shift clicked - not yet implemented');
  };

  return (
    <MaterialThemeProvider>
      <Box sx={{ flexGrow: 1, minHeight: '100vh', backgroundColor: 'background.default' }}>
        {/* Top App Bar */}
        <AppBar position="static" elevation={1}>
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 600 }}>
              Equilibrium
            </Typography>
            <IconButton
              size="large"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenu}
              color="inherit"
            >
              <AccountCircle />
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorEl)}
              onClose={handleClose}
            >
              <MenuItem onClick={handleLogout}>
                <LogoutIcon sx={{ mr: 1 }} />
                Logout
              </MenuItem>
            </Menu>
          </Toolbar>
        </AppBar>

        {/* Main Content */}
        <Container maxWidth="sm" sx={{ py: 4, px: 2 }}>
          <Stack spacing={4} alignItems="center">
            {/* Welcome Message */}
            <Box textAlign="center">
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 600,
                  color: 'primary.main',
                  mb: 1,
                  fontSize: { xs: '1.75rem', sm: '2.125rem' }
                }}
              >
                Welcome Back!
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ fontSize: { xs: '1rem', sm: '1.125rem' } }}
              >
                What would you like to do today?
              </Typography>
            </Box>

            {/* Main Action Buttons */}
            <Stack spacing={3} sx={{ width: '100%', maxWidth: 400 }}>
              {/* Create New Shift Button - Large, Primary, Disabled for now */}
              <Paper elevation={2} sx={{ borderRadius: 3 }}>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<AddIcon />}
                  onClick={handleCreateShift}
                  disabled
                  sx={{
                    width: '100%',
                    height: { xs: '80px', sm: '90px' },
                    fontSize: { xs: '1.1rem', sm: '1.25rem' },
                    fontWeight: 600,
                    borderRadius: 3,
                    textTransform: 'none',
                    '&.Mui-disabled': {
                      backgroundColor: 'action.disabledBackground',
                      color: 'action.disabled',
                    }
                  }}
                >
                  Create a New Shift
                </Button>
              </Paper>

              {/* Manage Shift Types Button - Smaller, Secondary */}
              <Button
                variant="outlined"
                size="large"
                startIcon={<SettingsIcon />}
                onClick={handleManageShiftTypes}
                sx={{
                  width: '100%',
                  height: { xs: '60px', sm: '70px' },
                  fontSize: { xs: '1rem', sm: '1.125rem' },
                  fontWeight: 500,
                  borderRadius: 3,
                  textTransform: 'none',
                  borderWidth: 2,
                  '&:hover': {
                    borderWidth: 2,
                  }
                }}
              >
                Manage Shift Types
              </Button>
            </Stack>

            {/* Info Text */}
            <Typography
              variant="body2"
              color="text.secondary"
              textAlign="center"
              sx={{
                mt: 4,
                fontSize: '0.875rem',
                fontStyle: 'italic'
              }}
            >
              Create shift functionality coming soon!
            </Typography>
          </Stack>
        </Container>
      </Box>
    </MaterialThemeProvider>
  );
}
