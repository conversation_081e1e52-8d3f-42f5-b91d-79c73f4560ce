"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import {
  Box,
  Container,
  Typography,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem,
  <PERSON>ack,
  Card,
  CardContent
} from '@mui/material';
import {
  Add as AddIcon,
  Settings as SettingsIcon,
  AccountCircle,
  Logout as LogoutIcon
} from '@mui/icons-material';
import MaterialThemeProvider from '@/components/ThemeProvider';

export default function Dashboard() {
  const router = useRouter();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleClose();
    // TODO: Implement actual logout logic
    console.log('Logout clicked');
    router.push('/login');
  };

  const handleManageShiftTypes = () => {
    router.push('/manage-shift-types');
  };

  const handleCreateShift = () => {
    // TODO: This will be implemented later
    console.log('Create shift clicked - not yet implemented');
  };

  return (
    <MaterialThemeProvider>
      <Box
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          backgroundColor: 'background.default'
        }}
      >
        {/* Top App Bar - Sticky with background matching page */}
        <AppBar
          position="sticky"
          elevation={0}
          sx={{
            backgroundColor: 'background.default',
            borderBottom: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Toolbar sx={{ py: 1 }}>
            {/* Logo */}
            <Box
              sx={{
                width: 32,
                height: 32,
                position: 'relative',
                mr: 2
              }}
            >
              <Image
                src="/equilibrium_logo.svg"
                alt="Equilibrium Logo"
                fill
                style={{ objectFit: 'contain' }}
                priority
              />
            </Box>

            {/* Title with primary color */}
            <Typography
              variant="h6"
              component="div"
              sx={{
                flexGrow: 1,
                fontWeight: 600,
                fontSize: '1.375rem',
                color: 'primary.main'
              }}
            >
              Equilibrium
            </Typography>

            {/* User icon - larger and better positioned */}
            <IconButton
              size="large"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenu}
              sx={{
                color: 'text.primary',
                width: 48,
                height: 48
              }}
            >
              <AccountCircle sx={{ fontSize: '2rem' }} />
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorEl)}
              onClose={handleClose}
              slotProps={{
                paper: {
                  sx: {
                    borderRadius: 2,
                    mt: 1,
                    minWidth: 120,
                  }
                }
              }}
            >
              <MenuItem onClick={handleLogout}>
                <LogoutIcon sx={{ mr: 1.5 }} />
                Logout
              </MenuItem>
            </Menu>
          </Toolbar>
        </AppBar>

        {/* Main Content */}
        <Container maxWidth="sm" sx={{ py: 3, px: 2 }}>
          <Stack spacing={3}>
            {/* Welcome Section */}
            <Box sx={{ textAlign: 'center', py: 2 }}>
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 400,
                  color: 'text.primary',
                  mb: 1,
                  fontSize: { xs: '1.75rem', sm: '2rem' }
                }}
              >
                Welcome Back!
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ fontSize: { xs: '1rem', sm: '1.125rem' } }}
              >
                What would you like to do today?
              </Typography>
            </Box>

            {/* Main Action Cards */}
            <Stack spacing={2}>
              {/* Create New Shift Card - Primary Action */}
              <Card elevation={1} sx={{ borderRadius: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<AddIcon />}
                    onClick={handleCreateShift}
                    disabled
                    fullWidth
                    sx={{
                      height: { xs: '56px', sm: '64px' },
                      fontSize: { xs: '1rem', sm: '1.125rem' },
                      fontWeight: 500,
                      textTransform: 'none',
                    }}
                  >
                    Create a New Shift
                  </Button>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mt: 2, textAlign: 'center', fontSize: '0.875rem' }}
                  >
                    Coming soon - Create and schedule your work shifts
                  </Typography>
                </CardContent>
              </Card>

              {/* Manage Shift Types Card - Secondary Action */}
              <Card elevation={1} sx={{ borderRadius: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <Button
                    variant="outlined"
                    size="large"
                    startIcon={<SettingsIcon />}
                    onClick={handleManageShiftTypes}
                    fullWidth
                    sx={{
                      height: { xs: '48px', sm: '56px' },
                      fontSize: { xs: '0.875rem', sm: '1rem' },
                      fontWeight: 500,
                      textTransform: 'none',
                    }}
                  >
                    Manage Shift Types
                  </Button>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mt: 2, textAlign: 'center', fontSize: '0.875rem' }}
                  >
                    Configure your custom shift types and settings
                  </Typography>
                </CardContent>
              </Card>
            </Stack>
          </Stack>
        </Container>
      </Box>
    </MaterialThemeProvider>
  );
}
