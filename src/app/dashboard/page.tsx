"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import {
  Box,
  Container,
  Typography,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Card,
  CardContent,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Settings as SettingsIcon,
  Menu as MenuIcon,
  AccountCircle,
  Logout as LogoutIcon
} from '@mui/icons-material';
import MaterialThemeProvider from '@/components/ThemeProvider';

export default function Dashboard() {
  const router = useRouter();
  const [drawerOpen, setDrawerOpen] = React.useState(false);

  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleLogout = () => {
    setDrawerOpen(false);
    router.push('/login');
  };

  const handleManageShiftTypes = () => {
    router.push('/manage-shift-types');
  };

  const handleCreateShift = () => {
    // TODO: This will be implemented later
    console.log('Create shift clicked - not yet implemented');
  };

  return (
    <MaterialThemeProvider>
      <Box
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          backgroundColor: 'background.default'
        }}
      >
        {/* Top App Bar - Ultra light like reference image */}
        <AppBar
          position="sticky"
          elevation={0}
          sx={{
            backgroundColor: 'background.default',
            borderBottom: '1px solid rgba(0,0,0,0.08)', // Ultra light line
            boxShadow: 'none'
          }}
        >
          <Toolbar sx={{ minHeight: { xs: 56, sm: 64 } }}>
            {/* Hamburger Menu */}
            <IconButton
              edge="start"
              color="inherit"
              aria-label="menu"
              onClick={handleDrawerToggle}
              sx={{
                mr: { xs: 2, sm: 3 },
                color: 'text.primary'
              }}
            >
              <MenuIcon />
            </IconButton>

            {/* Logo and Title - Centered on mobile, left on desktop */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                flexGrow: { xs: 1, sm: 0 },
                justifyContent: { xs: 'center', sm: 'flex-start' },
                mr: { xs: 0, sm: 'auto' }
              }}
            >
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  position: 'relative',
                  mr: 2
                }}
              >
                <Image
                  src="/equilibrium_logo.svg"
                  alt="Equilibrium Logo"
                  fill
                  style={{ objectFit: 'contain' }}
                  priority
                />
              </Box>

              <Typography
                variant="h6"
                component="div"
                sx={{
                  fontWeight: 600,
                  fontSize: '1.375rem',
                  color: 'primary.main'
                }}
              >
                Equilibrium
              </Typography>
            </Box>
          </Toolbar>
        </AppBar>

        {/* Hamburger Menu Drawer */}
        <Drawer
          anchor="left"
          open={drawerOpen}
          onClose={handleDrawerToggle}
          sx={{
            '& .MuiDrawer-paper': {
              width: 280,
              boxSizing: 'border-box',
            },
          }}
        >
          <Box sx={{ p: 3 }}>
            {/* User Profile Section */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar sx={{ width: 48, height: 48, mr: 2, bgcolor: 'primary.main' }}>
                <AccountCircle sx={{ fontSize: '2rem' }} />
              </Avatar>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 500 }}>
                  Mario Rossi
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <EMAIL>
                </Typography>
              </Box>
            </Box>

            <Divider sx={{ my: 2 }} />

            {/* Menu Items */}
            <List>
              <ListItem
                component="button"
                onClick={handleLogout}
                sx={{
                  borderRadius: 2,
                  '&:hover': {
                    backgroundColor: 'rgba(211, 47, 47, 0.08)',
                  }
                }}
              >
                <ListItemIcon>
                  <LogoutIcon color="error" />
                </ListItemIcon>
                <ListItemText
                  primary="Logout"
                  primaryTypographyProps={{
                    color: 'error.main',
                    fontWeight: 500
                  }}
                />
              </ListItem>
            </List>
          </Box>
        </Drawer>

        {/* Main Content */}
        <Container maxWidth="sm" sx={{ py: 3, px: 2 }}>
          <Stack spacing={3}>
            {/* Welcome Section */}
            <Box sx={{ textAlign: 'center', py: 2 }}>
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 400,
                  color: 'text.primary',
                  mb: 1,
                  fontSize: { xs: '1.75rem', sm: '2rem' }
                }}
              >
                Welcome Back!
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ fontSize: { xs: '1rem', sm: '1.125rem' } }}
              >
                What would you like to do today?
              </Typography>
            </Box>

            {/* Main Action Cards */}
            <Stack spacing={2}>
              {/* Create New Shift Card - Primary Action */}
              <Card elevation={1} sx={{ borderRadius: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<AddIcon />}
                    onClick={handleCreateShift}
                    disabled
                    fullWidth
                    sx={{
                      height: { xs: '56px', sm: '64px' },
                      fontSize: { xs: '1rem', sm: '1.125rem' },
                      fontWeight: 500,
                      textTransform: 'none',
                    }}
                  >
                    Create a New Shift
                  </Button>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mt: 2, textAlign: 'center', fontSize: '0.875rem' }}
                  >
                    Coming soon - Create and schedule your work shifts
                  </Typography>
                </CardContent>
              </Card>

              {/* Manage Shift Types Card - Secondary Action */}
              <Card elevation={1} sx={{ borderRadius: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <Button
                    variant="outlined"
                    size="large"
                    startIcon={<SettingsIcon />}
                    onClick={handleManageShiftTypes}
                    fullWidth
                    sx={{
                      height: { xs: '48px', sm: '56px' },
                      fontSize: { xs: '0.875rem', sm: '1rem' },
                      fontWeight: 500,
                      textTransform: 'none',
                    }}
                  >
                    Manage Shift Types
                  </Button>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mt: 2, textAlign: 'center', fontSize: '0.875rem' }}
                  >
                    Configure your custom shift types and settings
                  </Typography>
                </CardContent>
              </Card>
            </Stack>
          </Stack>
        </Container>
      </Box>
    </MaterialThemeProvider>
  );
}
