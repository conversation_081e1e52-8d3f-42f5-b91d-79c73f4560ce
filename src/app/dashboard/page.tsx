"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Typo<PERSON>,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Paper
} from '@mui/material';
import {
  Add as AddIcon,
  Settings as SettingsIcon,
  AccountCircle,
  Logout as LogoutIcon
} from '@mui/icons-material';
import MaterialThemeProvider from '@/components/ThemeProvider';

export default function Dashboard() {
  const router = useRouter();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleClose();
    // TODO: Implement actual logout logic
    console.log('Logout clicked');
    router.push('/login');
  };

  const handleManageShiftTypes = () => {
    router.push('/manage-shift-types');
  };

  const handleCreateShift = () => {
    // TODO: This will be implemented later
    console.log('Create shift clicked - not yet implemented');
  };

  return (
    <MaterialThemeProvider>
      <Box
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          position: 'relative'
        }}
      >
        {/* Top App Bar */}
        <AppBar position="static" elevation={0} sx={{ backgroundColor: 'transparent' }}>
          <Toolbar sx={{ py: 1 }}>
            <Typography
              variant="h6"
              component="div"
              sx={{
                flexGrow: 1,
                fontWeight: 600,
                fontSize: { xs: '1.25rem', sm: '1.5rem' }
              }}
            >
              Equilibrium
            </Typography>
            <IconButton
              size="large"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenu}
              color="inherit"
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                }
              }}
            >
              <AccountCircle />
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorEl)}
              onClose={handleClose}
              slotProps={{
                paper: {
                  sx: {
                    borderRadius: 3,
                    mt: 1,
                    minWidth: 120,
                  }
                }
              }}
            >
              <MenuItem onClick={handleLogout} sx={{ borderRadius: 2, mx: 1, my: 0.5 }}>
                <LogoutIcon sx={{ mr: 1.5, fontSize: '1.25rem' }} />
                <Typography variant="body2">Logout</Typography>
              </MenuItem>
            </Menu>
          </Toolbar>
        </AppBar>

        {/* Main Content */}
        <Container maxWidth="sm" sx={{ py: 4, px: 3 }}>
          {/* Main Card Container */}
          <Paper
            elevation={8}
            sx={{
              borderRadius: 6,
              p: { xs: 4, sm: 5 },
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(20px)',
              textAlign: 'center',
            }}
          >
            <Stack spacing={4} alignItems="center">
              {/* Welcome Message */}
              <Box>
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 600,
                    color: 'primary.main',
                    mb: 1.5,
                    fontSize: { xs: '1.75rem', sm: '2.125rem' }
                  }}
                >
                  Welcome Back!
                </Typography>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '1rem', sm: '1.125rem' },
                    fontWeight: 400
                  }}
                >
                  What would you like to do today?
                </Typography>
              </Box>

              {/* Main Action Buttons */}
              <Stack spacing={3} sx={{ width: '100%', maxWidth: 380 }}>
                {/* Create New Shift Button - Large, Primary, Disabled for now */}
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<AddIcon sx={{ fontSize: '1.5rem' }} />}
                  onClick={handleCreateShift}
                  disabled
                  sx={{
                    width: '100%',
                    height: { xs: '72px', sm: '80px' },
                    fontSize: { xs: '1.125rem', sm: '1.25rem' },
                    fontWeight: 500,
                    borderRadius: 6,
                    textTransform: 'none',
                    boxShadow: 'none',
                    '&.Mui-disabled': {
                      backgroundColor: 'grey.200',
                      color: 'grey.500',
                      '& .MuiSvgIcon-root': {
                        color: 'grey.400',
                      }
                    }
                  }}
                >
                  Create a New Shift
                </Button>

                {/* Manage Shift Types Button - Smaller, Outlined */}
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<SettingsIcon sx={{ fontSize: '1.25rem' }} />}
                  onClick={handleManageShiftTypes}
                  sx={{
                    width: '100%',
                    height: { xs: '56px', sm: '64px' },
                    fontSize: { xs: '1rem', sm: '1.125rem' },
                    fontWeight: 500,
                    borderRadius: 6,
                    textTransform: 'none',
                    borderWidth: 2,
                    '&:hover': {
                      borderWidth: 2,
                      backgroundColor: 'rgba(66, 133, 244, 0.04)',
                    }
                  }}
                >
                  Manage Shift Types
                </Button>
              </Stack>

              {/* Info Text */}
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  mt: 2,
                  fontSize: '0.875rem',
                  fontStyle: 'italic',
                  opacity: 0.8
                }}
              >
                Create shift functionality coming soon!
              </Typography>
            </Stack>
          </Paper>
        </Container>
      </Box>
    </MaterialThemeProvider>
  );
}
