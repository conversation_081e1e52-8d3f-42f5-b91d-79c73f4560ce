"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Typography,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Stack,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Fab
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import MaterialThemeProvider from '@/components/ThemeProvider';

// Mock data for shift types
const mockShiftTypes = [
  { id: 1, name: 'Morning Shift', duration: '8 hours', color: '#1976d2' },
  { id: 2, name: 'Evening Shift', duration: '8 hours', color: '#dc004e' },
  { id: 3, name: 'Night Shift', duration: '12 hours', color: '#7b1fa2' },
];

export default function ManageShiftTypes() {
  const router = useRouter();

  const handleBack = () => {
    router.push('/dashboard');
  };

  const handleAddShiftType = () => {
    // TODO: Implement add shift type functionality
    console.log('Add shift type clicked');
  };

  const handleEditShiftType = (id: number) => {
    // TODO: Implement edit shift type functionality
    console.log('Edit shift type clicked for ID:', id);
  };

  const handleDeleteShiftType = (id: number) => {
    // TODO: Implement delete shift type functionality
    console.log('Delete shift type clicked for ID:', id);
  };

  return (
    <MaterialThemeProvider>
      <Box
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          position: 'relative'
        }}
      >
        {/* Top App Bar */}
        <AppBar position="static" elevation={0} sx={{ backgroundColor: 'transparent' }}>
          <Toolbar sx={{ py: 1 }}>
            <IconButton
              edge="start"
              color="inherit"
              aria-label="back"
              onClick={handleBack}
              sx={{
                mr: 2,
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                }
              }}
            >
              <ArrowBackIcon />
            </IconButton>
            <Typography
              variant="h6"
              component="div"
              sx={{
                flexGrow: 1,
                fontWeight: 600,
                fontSize: { xs: '1.25rem', sm: '1.5rem' }
              }}
            >
              Manage Shift Types
            </Typography>
          </Toolbar>
        </AppBar>

        {/* Main Content */}
        <Container maxWidth="md" sx={{ py: 4, px: 3, pb: 12 }}>
          {/* Main Card Container */}
          <Paper
            elevation={8}
            sx={{
              borderRadius: 6,
              p: { xs: 3, sm: 4 },
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(20px)',
            }}
          >
            <Stack spacing={4}>
              {/* Header */}
              <Box textAlign="center">
                <Typography
                  variant="h5"
                  component="h1"
                  sx={{
                    fontWeight: 600,
                    color: 'primary.main',
                    mb: 1.5,
                    fontSize: { xs: '1.5rem', sm: '1.75rem' }
                  }}
                >
                  Your Shift Types
                </Typography>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.95rem', sm: '1rem' },
                    fontWeight: 400
                  }}
                >
                  Create and manage your custom shift types
                </Typography>
              </Box>

              {/* Shift Types List */}
              {mockShiftTypes.length > 0 ? (
                <Paper
                  elevation={0}
                  sx={{
                    borderRadius: 4,
                    border: '1px solid',
                    borderColor: 'grey.200',
                    overflow: 'hidden'
                  }}
                >
                  <List sx={{ py: 0 }}>
                    {mockShiftTypes.map((shiftType, index) => (
                      <React.Fragment key={shiftType.id}>
                        <ListItem
                          sx={{
                            py: 3,
                            px: 3,
                            '&:hover': {
                              backgroundColor: 'rgba(66, 133, 244, 0.04)',
                            }
                          }}
                        >
                          <Box
                            sx={{
                              width: 20,
                              height: 20,
                              borderRadius: '50%',
                              backgroundColor: shiftType.color,
                              mr: 2.5,
                              flexShrink: 0,
                              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}
                          />
                          <ListItemText
                            primary={
                              <Typography
                                variant="h6"
                                sx={{
                                  fontSize: { xs: '1.125rem', sm: '1.25rem' },
                                  fontWeight: 500,
                                  mb: 0.5
                                }}
                              >
                                {shiftType.name}
                              </Typography>
                            }
                            secondary={
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ fontSize: { xs: '0.875rem', sm: '0.95rem' } }}
                              >
                                Duration: {shiftType.duration}
                              </Typography>
                            }
                          />
                          <ListItemSecondaryAction>
                            <Stack direction="row" spacing={1}>
                              <IconButton
                                edge="end"
                                aria-label="edit"
                                onClick={() => handleEditShiftType(shiftType.id)}
                                size="medium"
                                sx={{
                                  color: 'primary.main',
                                  backgroundColor: 'rgba(66, 133, 244, 0.08)',
                                  '&:hover': {
                                    backgroundColor: 'primary.main',
                                    color: 'white'
                                  }
                                }}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                              <IconButton
                                edge="end"
                                aria-label="delete"
                                onClick={() => handleDeleteShiftType(shiftType.id)}
                                size="medium"
                                sx={{
                                  color: 'error.main',
                                  backgroundColor: 'rgba(244, 67, 54, 0.08)',
                                  '&:hover': {
                                    backgroundColor: 'error.main',
                                    color: 'white'
                                  }
                                }}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Stack>
                          </ListItemSecondaryAction>
                        </ListItem>
                        {index < mockShiftTypes.length - 1 && (
                          <Box sx={{ borderBottom: 1, borderColor: 'grey.200', mx: 3 }} />
                        )}
                      </React.Fragment>
                    ))}
                  </List>
                </Paper>
              ) : (
                <Paper
                  elevation={0}
                  sx={{
                    p: 5,
                    textAlign: 'center',
                    borderRadius: 4,
                    backgroundColor: 'grey.50',
                    border: '1px solid',
                    borderColor: 'grey.200'
                  }}
                >
                  <Typography
                    variant="h6"
                    color="text.secondary"
                    sx={{ mb: 1, fontSize: { xs: '1.125rem', sm: '1.25rem' } }}
                  >
                    No shift types yet
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ fontSize: { xs: '0.875rem', sm: '0.95rem' } }}
                  >
                    Create your first shift type to get started
                  </Typography>
                </Paper>
              )}

              {/* Info Text */}
              <Typography
                variant="body2"
                color="text.secondary"
                textAlign="center"
                sx={{
                  mt: 2,
                  fontSize: '0.875rem',
                  fontStyle: 'italic',
                  opacity: 0.8
                }}
              >
                Shift type management functionality coming soon!
              </Typography>
            </Stack>
          </Paper>
        </Container>

        {/* Floating Action Button */}
        <Fab
          color="primary"
          aria-label="add shift type"
          onClick={handleAddShiftType}
          sx={{
            position: 'fixed',
            bottom: { xs: 24, sm: 32 },
            right: { xs: 24, sm: 32 },
            width: { xs: 64, sm: 72 },
            height: { xs: 64, sm: 72 },
            boxShadow: '0 8px 24px rgba(66, 133, 244, 0.3)',
            '&:hover': {
              boxShadow: '0 12px 32px rgba(66, 133, 244, 0.4)',
              transform: 'scale(1.05)',
            },
            transition: 'all 0.2s ease-in-out'
          }}
        >
          <AddIcon sx={{ fontSize: { xs: '1.75rem', sm: '2rem' } }} />
        </Fab>
      </Box>
    </MaterialThemeProvider>
  );
}
