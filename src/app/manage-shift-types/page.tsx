"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import {
  Box,
  Container,
  Typography,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Stack,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Fab,
  Drawer,
  ListItemIcon,
  Avatar,
  Divider
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Menu as MenuIcon,
  AccountCircle,
  Logout as LogoutIcon
} from '@mui/icons-material';
import MaterialThemeProvider from '@/components/ThemeProvider';

// Mock data for shift types
const mockShiftTypes = [
  { id: 1, name: 'Morning Shift', duration: '8 hours', color: '#1976d2' },
  { id: 2, name: 'Evening Shift', duration: '8 hours', color: '#dc004e' },
  { id: 3, name: 'Night Shift', duration: '12 hours', color: '#7b1fa2' },
];

export default function ManageShiftTypes() {
  const router = useRouter();
  const [drawerOpen, setDrawerOpen] = React.useState(false);

  const handleBack = () => {
    router.push('/dashboard');
  };

  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleLogout = () => {
    setDrawerOpen(false);
    router.push('/login');
  };

  const handleAddShiftType = () => {
    // TODO: Implement add shift type functionality
    console.log('Add shift type clicked');
  };

  const handleEditShiftType = (id: number) => {
    // TODO: Implement edit shift type functionality
    console.log('Edit shift type clicked for ID:', id);
  };

  const handleDeleteShiftType = (id: number) => {
    // TODO: Implement delete shift type functionality
    console.log('Delete shift type clicked for ID:', id);
  };

  return (
    <MaterialThemeProvider>
      <Box
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          backgroundColor: 'background.default'
        }}
      >
        {/* Top App Bar - Ultra light like reference image */}
        <AppBar
          position="sticky"
          elevation={0}
          sx={{
            backgroundColor: 'background.default',
            borderBottom: '1px solid rgba(0,0,0,0.08)', // Ultra light line
            boxShadow: 'none'
          }}
        >
          <Toolbar sx={{ minHeight: { xs: 56, sm: 64 } }}>
            {/* Back Button */}
            <IconButton
              edge="start"
              aria-label="back"
              onClick={handleBack}
              sx={{
                mr: { xs: 1, sm: 2 },
                color: 'text.primary'
              }}
            >
              <ArrowBackIcon />
            </IconButton>

            {/* Hamburger Menu */}
            <IconButton
              color="inherit"
              aria-label="menu"
              onClick={handleDrawerToggle}
              sx={{
                mr: { xs: 2, sm: 3 },
                color: 'text.primary'
              }}
            >
              <MenuIcon />
            </IconButton>

            {/* Logo and Title - Centered on mobile, left on desktop */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                flexGrow: { xs: 1, sm: 0 },
                justifyContent: { xs: 'center', sm: 'flex-start' },
                mr: { xs: 0, sm: 'auto' }
              }}
            >
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  position: 'relative',
                  mr: 2
                }}
              >
                <Image
                  src="/equilibrium_logo.svg"
                  alt="Equilibrium Logo"
                  fill
                  style={{ objectFit: 'contain' }}
                  priority
                />
              </Box>

              <Typography
                variant="h6"
                component="div"
                sx={{
                  fontWeight: 600,
                  fontSize: '1.375rem',
                  color: 'primary.main'
                }}
              >
                Manage Shift Types
              </Typography>
            </Box>
          </Toolbar>
        </AppBar>

        {/* Hamburger Menu Drawer */}
        <Drawer
          anchor="left"
          open={drawerOpen}
          onClose={handleDrawerToggle}
          sx={{
            '& .MuiDrawer-paper': {
              width: 280,
              boxSizing: 'border-box',
            },
          }}
        >
          <Box sx={{ p: 3 }}>
            {/* User Profile Section */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar sx={{ width: 48, height: 48, mr: 2, bgcolor: 'primary.main' }}>
                <AccountCircle sx={{ fontSize: '2rem' }} />
              </Avatar>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 500 }}>
                  Mario Rossi
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <EMAIL>
                </Typography>
              </Box>
            </Box>

            <Divider sx={{ my: 2 }} />

            {/* Menu Items */}
            <List>
              <ListItem
                component="button"
                onClick={handleLogout}
                sx={{
                  borderRadius: 2,
                  '&:hover': {
                    backgroundColor: 'rgba(211, 47, 47, 0.08)',
                  }
                }}
              >
                <ListItemIcon>
                  <LogoutIcon color="error" />
                </ListItemIcon>
                <ListItemText
                  primary="Logout"
                  primaryTypographyProps={{
                    color: 'error.main',
                    fontWeight: 500
                  }}
                />
              </ListItem>
            </List>
          </Box>
        </Drawer>

        {/* Main Content */}
        <Container maxWidth="md" sx={{ py: 3, px: 2, pb: 10 }}>
          <Stack spacing={3}>
            {/* Header */}
            <Box sx={{ textAlign: 'center', py: 2 }}>
              <Typography
                variant="h5"
                component="h1"
                sx={{
                  fontWeight: 400,
                  color: 'text.primary',
                  mb: 1,
                  fontSize: { xs: '1.5rem', sm: '1.75rem' }
                }}
              >
                Your Shift Types
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ fontSize: { xs: '0.95rem', sm: '1rem' } }}
              >
                Create and manage your custom shift types
              </Typography>
            </Box>

            {/* Shift Types List */}
            {mockShiftTypes.length > 0 ? (
              <Paper elevation={1} sx={{ borderRadius: 3, overflow: 'hidden' }}>
                <List sx={{ py: 0 }}>
                  {mockShiftTypes.map((shiftType, index) => (
                    <React.Fragment key={shiftType.id}>
                      <ListItem
                        sx={{
                          py: 2,
                          px: 3,
                          '&:hover': {
                            backgroundColor: 'rgba(103, 80, 164, 0.08)',
                          }
                        }}
                      >
                        <Box
                          sx={{
                            width: 16,
                            height: 16,
                            borderRadius: '50%',
                            backgroundColor: shiftType.color,
                            mr: 2,
                            flexShrink: 0
                          }}
                        />
                        <ListItemText
                          primary={
                            <Typography
                              variant="h6"
                              sx={{
                                fontSize: { xs: '1rem', sm: '1.125rem' },
                                fontWeight: 500
                              }}
                            >
                              {shiftType.name}
                            </Typography>
                          }
                          secondary={
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{ fontSize: { xs: '0.875rem', sm: '0.95rem' } }}
                            >
                              Duration: {shiftType.duration}
                            </Typography>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Stack direction="row" spacing={1}>
                            <IconButton
                              edge="end"
                              aria-label="edit"
                              onClick={() => handleEditShiftType(shiftType.id)}
                              size="small"
                              sx={{
                                color: 'primary.main',
                                '&:hover': {
                                  backgroundColor: 'rgba(103, 80, 164, 0.08)',
                                }
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              edge="end"
                              aria-label="delete"
                              onClick={() => handleDeleteShiftType(shiftType.id)}
                              size="small"
                              sx={{
                                color: 'error.main',
                                '&:hover': {
                                  backgroundColor: 'rgba(211, 47, 47, 0.08)',
                                }
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Stack>
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < mockShiftTypes.length - 1 && (
                        <Box sx={{ borderBottom: 1, borderColor: 'divider', mx: 2 }} />
                      )}
                    </React.Fragment>
                  ))}
                </List>
              </Paper>
            ) : (
              <Paper
                elevation={1}
                sx={{
                  p: 4,
                  textAlign: 'center',
                  borderRadius: 3,
                  backgroundColor: 'grey.50'
                }}
              >
                <Typography
                  variant="h6"
                  color="text.secondary"
                  sx={{ mb: 1, fontSize: { xs: '1rem', sm: '1.125rem' } }}
                >
                  No shift types yet
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ fontSize: { xs: '0.875rem', sm: '0.95rem' } }}
                >
                  Create your first shift type to get started
                </Typography>
              </Paper>
            )}

            {/* Info Text */}
            <Typography
              variant="body2"
              color="text.secondary"
              textAlign="center"
              sx={{
                fontSize: '0.875rem',
                fontStyle: 'italic'
              }}
            >
              Shift type management functionality coming soon!
            </Typography>
          </Stack>
        </Container>

        {/* Floating Action Button */}
        <Fab
          color="primary"
          aria-label="add shift type"
          onClick={handleAddShiftType}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            width: 56,
            height: 56,
          }}
        >
          <AddIcon />
        </Fab>
      </Box>
    </MaterialThemeProvider>
  );
}
