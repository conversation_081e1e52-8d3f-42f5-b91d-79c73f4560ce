"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import {
  Box,
  Container,
  Typography,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Stack,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Fab
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import MaterialThemeProvider from '@/components/ThemeProvider';

// Mock data for shift types
const mockShiftTypes = [
  { id: 1, name: 'Morning Shift', duration: '8 hours', color: '#1976d2' },
  { id: 2, name: 'Evening Shift', duration: '8 hours', color: '#dc004e' },
  { id: 3, name: 'Night Shift', duration: '12 hours', color: '#7b1fa2' },
];

export default function ManageShiftTypes() {
  const router = useRouter();

  const handleBack = () => {
    router.push('/dashboard');
  };

  const handleAddShiftType = () => {
    // TODO: Implement add shift type functionality
    console.log('Add shift type clicked');
  };

  const handleEditShiftType = (id: number) => {
    // TODO: Implement edit shift type functionality
    console.log('Edit shift type clicked for ID:', id);
  };

  const handleDeleteShiftType = (id: number) => {
    // TODO: Implement delete shift type functionality
    console.log('Delete shift type clicked for ID:', id);
  };

  return (
    <MaterialThemeProvider>
      <Box
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          backgroundColor: 'background.default'
        }}
      >
        {/* Top App Bar - Sticky with background matching page */}
        <AppBar
          position="sticky"
          elevation={0}
          sx={{
            backgroundColor: 'background.default',
            borderBottom: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Toolbar sx={{ py: 1 }}>
            <IconButton
              edge="start"
              aria-label="back"
              onClick={handleBack}
              sx={{
                mr: 2,
                color: 'text.primary',
                width: 48,
                height: 48
              }}
            >
              <ArrowBackIcon sx={{ fontSize: '1.5rem' }} />
            </IconButton>

            {/* Logo */}
            <Box
              sx={{
                width: 32,
                height: 32,
                position: 'relative',
                mr: 2
              }}
            >
              <Image
                src="/equilibrium_logo.svg"
                alt="Equilibrium Logo"
                fill
                style={{ objectFit: 'contain' }}
                priority
              />
            </Box>

            {/* Title with primary color */}
            <Typography
              variant="h6"
              component="div"
              sx={{
                flexGrow: 1,
                fontWeight: 600,
                fontSize: '1.375rem',
                color: 'primary.main'
              }}
            >
              Manage Shift Types
            </Typography>
          </Toolbar>
        </AppBar>

        {/* Main Content */}
        <Container maxWidth="md" sx={{ py: 3, px: 2, pb: 10 }}>
          <Stack spacing={3}>
            {/* Header */}
            <Box sx={{ textAlign: 'center', py: 2 }}>
              <Typography
                variant="h5"
                component="h1"
                sx={{
                  fontWeight: 400,
                  color: 'text.primary',
                  mb: 1,
                  fontSize: { xs: '1.5rem', sm: '1.75rem' }
                }}
              >
                Your Shift Types
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ fontSize: { xs: '0.95rem', sm: '1rem' } }}
              >
                Create and manage your custom shift types
              </Typography>
            </Box>

            {/* Shift Types List */}
            {mockShiftTypes.length > 0 ? (
              <Paper elevation={1} sx={{ borderRadius: 3, overflow: 'hidden' }}>
                <List sx={{ py: 0 }}>
                  {mockShiftTypes.map((shiftType, index) => (
                    <React.Fragment key={shiftType.id}>
                      <ListItem
                        sx={{
                          py: 2,
                          px: 3,
                          '&:hover': {
                            backgroundColor: 'rgba(103, 80, 164, 0.08)',
                          }
                        }}
                      >
                        <Box
                          sx={{
                            width: 16,
                            height: 16,
                            borderRadius: '50%',
                            backgroundColor: shiftType.color,
                            mr: 2,
                            flexShrink: 0
                          }}
                        />
                        <ListItemText
                          primary={
                            <Typography
                              variant="h6"
                              sx={{
                                fontSize: { xs: '1rem', sm: '1.125rem' },
                                fontWeight: 500
                              }}
                            >
                              {shiftType.name}
                            </Typography>
                          }
                          secondary={
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{ fontSize: { xs: '0.875rem', sm: '0.95rem' } }}
                            >
                              Duration: {shiftType.duration}
                            </Typography>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Stack direction="row" spacing={1}>
                            <IconButton
                              edge="end"
                              aria-label="edit"
                              onClick={() => handleEditShiftType(shiftType.id)}
                              size="small"
                              sx={{
                                color: 'primary.main',
                                '&:hover': {
                                  backgroundColor: 'rgba(103, 80, 164, 0.08)',
                                }
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              edge="end"
                              aria-label="delete"
                              onClick={() => handleDeleteShiftType(shiftType.id)}
                              size="small"
                              sx={{
                                color: 'error.main',
                                '&:hover': {
                                  backgroundColor: 'rgba(211, 47, 47, 0.08)',
                                }
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Stack>
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < mockShiftTypes.length - 1 && (
                        <Box sx={{ borderBottom: 1, borderColor: 'divider', mx: 2 }} />
                      )}
                    </React.Fragment>
                  ))}
                </List>
              </Paper>
            ) : (
              <Paper
                elevation={1}
                sx={{
                  p: 4,
                  textAlign: 'center',
                  borderRadius: 3,
                  backgroundColor: 'grey.50'
                }}
              >
                <Typography
                  variant="h6"
                  color="text.secondary"
                  sx={{ mb: 1, fontSize: { xs: '1rem', sm: '1.125rem' } }}
                >
                  No shift types yet
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ fontSize: { xs: '0.875rem', sm: '0.95rem' } }}
                >
                  Create your first shift type to get started
                </Typography>
              </Paper>
            )}

            {/* Info Text */}
            <Typography
              variant="body2"
              color="text.secondary"
              textAlign="center"
              sx={{
                fontSize: '0.875rem',
                fontStyle: 'italic'
              }}
            >
              Shift type management functionality coming soon!
            </Typography>
          </Stack>
        </Container>

        {/* Floating Action Button */}
        <Fab
          color="primary"
          aria-label="add shift type"
          onClick={handleAddShiftType}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            width: 56,
            height: 56,
          }}
        >
          <AddIcon />
        </Fab>
      </Box>
    </MaterialThemeProvider>
  );
}
