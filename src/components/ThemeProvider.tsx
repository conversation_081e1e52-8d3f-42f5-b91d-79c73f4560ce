"use client";

import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// Material Design 3 theme matching login page
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#4285f4', // Google Blue matching login
      light: '#6fa8f7',
      dark: '#3367d6',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#667eea', // Purple gradient start
      light: '#8a9ff0',
      dark: '#4c5ed1',
      contrastText: '#ffffff',
    },
    background: {
      default: '#f8f9ff', // Very light purple-tinted background
      paper: '#ffffff',
    },
    grey: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
    },
  },
  typography: {
    fontFamily: 'var(--font-inter), "Google Sans", Robot<PERSON>, Arial, sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
      letterSpacing: '-0.02em',
    },
    h2: {
      fontSize: '1.75rem',
      fontWeight: 500,
      letterSpacing: '-0.01em',
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      letterSpacing: '-0.01em',
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      letterSpacing: '0em',
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 500,
      letterSpacing: '0em',
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      letterSpacing: '0.01em',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      letterSpacing: '0.01em',
      lineHeight: 1.5,
    },
  },
  shape: {
    borderRadius: 24, // More rounded like login page
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 24,
          padding: '14px 28px',
          fontSize: '1rem',
          fontWeight: 500,
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 2px 8px rgba(0,0,0,0.12)',
          },
        },
        contained: {
          background: 'linear-gradient(135deg, #4285f4 0%, #667eea 100%)',
          color: '#ffffff',
          '&:hover': {
            background: 'linear-gradient(135deg, #3367d6 0%, #5a6fd8 100%)',
            boxShadow: '0 4px 12px rgba(66, 133, 244, 0.3)',
          },
          '&:disabled': {
            background: '#e5e7eb',
            color: '#9ca3af',
          },
        },
        outlined: {
          borderWidth: 2,
          borderColor: '#4285f4',
          color: '#4285f4',
          '&:hover': {
            borderWidth: 2,
            backgroundColor: 'rgba(66, 133, 244, 0.04)',
            borderColor: '#3367d6',
          },
        },
        sizeLarge: {
          padding: '16px 32px',
          fontSize: '1.125rem',
          minHeight: '56px',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          borderRadius: 24,
          boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
        },
        elevation1: {
          boxShadow: '0 1px 6px rgba(0,0,0,0.06)',
        },
        elevation2: {
          boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
        },
        elevation8: {
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          background: 'linear-gradient(135deg, #4285f4 0%, #667eea 100%)',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
    MuiFab: {
      styleOverrides: {
        root: {
          background: 'linear-gradient(135deg, #4285f4 0%, #667eea 100%)',
          boxShadow: '0 4px 16px rgba(66, 133, 244, 0.3)',
          '&:hover': {
            background: 'linear-gradient(135deg, #3367d6 0%, #5a6fd8 100%)',
            boxShadow: '0 6px 20px rgba(66, 133, 244, 0.4)',
          },
        },
      },
    },
    MuiListItem: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          margin: '4px 8px',
          '&:hover': {
            backgroundColor: 'rgba(66, 133, 244, 0.04)',
          },
        },
      },
    },
  },
});

interface MaterialThemeProviderProps {
  children: React.ReactNode;
}

export default function MaterialThemeProvider({ children }: MaterialThemeProviderProps) {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
}
