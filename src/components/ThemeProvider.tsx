"use client";

import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// Material Design 3 theme - proper implementation
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#6750A4', // Material Design 3 primary
      light: '#7F67BE',
      dark: '#4F378B',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#625B71',
      light: '#7A7289',
      dark: '#4A4458',
      contrastText: '#ffffff',
    },
    background: {
      default: '#FFFBFE', // Material Design 3 surface
      paper: '#FFFBFE',
    },
    text: {
      primary: '#1C1B1F',
      secondary: '#49454F',
    },
    divider: '#CAC4D0',
    grey: {
      50: '#FEFBFF',
      100: '#F4EFF4',
      200: '#E7E0EC',
      300: '#CAC4D0',
      400: '#79747E',
      500: '#49454F',
      600: '#1C1B1F',
    },
  },
  typography: {
    fontFamily: 'var(--font-inter), "Google Sans", Robot<PERSON>, Arial, sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
      letterSpacing: '-0.02em',
    },
    h2: {
      fontSize: '1.75rem',
      fontWeight: 500,
      letterSpacing: '-0.01em',
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      letterSpacing: '-0.01em',
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      letterSpacing: '0em',
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 500,
      letterSpacing: '0em',
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      letterSpacing: '0.01em',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      letterSpacing: '0.01em',
      lineHeight: 1.5,
    },
  },
  shape: {
    borderRadius: 12, // Material Design 3 standard
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 20, // Material Design 3 full corner
          fontWeight: 500,
          boxShadow: 'none',
          minHeight: 40,
        },
        contained: {
          backgroundColor: '#6750A4',
          color: '#ffffff',
          '&:hover': {
            backgroundColor: '#4F378B',
            boxShadow: '0 1px 2px rgba(0,0,0,0.3), 0 1px 3px 1px rgba(0,0,0,0.15)',
          },
          '&:disabled': {
            backgroundColor: '#E7E0EC',
            color: '#79747E',
          },
        },
        outlined: {
          borderColor: '#79747E',
          color: '#6750A4',
          '&:hover': {
            backgroundColor: 'rgba(103, 80, 164, 0.08)',
            borderColor: '#6750A4',
          },
        },
        text: {
          color: '#6750A4',
          '&:hover': {
            backgroundColor: 'rgba(103, 80, 164, 0.08)',
          },
        },
        sizeLarge: {
          padding: '10px 24px',
          fontSize: '0.875rem',
          minHeight: 40,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backgroundColor: '#FFFBFE',
        },
        elevation0: {
          boxShadow: 'none',
          backgroundColor: 'transparent',
        },
        elevation1: {
          boxShadow: '0 1px 2px rgba(0,0,0,0.3), 0 1px 3px 1px rgba(0,0,0,0.15)',
        },
        elevation2: {
          boxShadow: '0 1px 2px rgba(0,0,0,0.3), 0 2px 6px 2px rgba(0,0,0,0.15)',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#6750A4',
          boxShadow: '0 1px 2px rgba(0,0,0,0.3), 0 1px 3px 1px rgba(0,0,0,0.15)',
        },
      },
    },
    MuiFab: {
      styleOverrides: {
        root: {
          backgroundColor: '#6750A4',
          color: '#ffffff',
          boxShadow: '0 1px 2px rgba(0,0,0,0.3), 0 2px 6px 2px rgba(0,0,0,0.15)',
          '&:hover': {
            backgroundColor: '#4F378B',
            boxShadow: '0 1px 2px rgba(0,0,0,0.3), 0 2px 6px 2px rgba(0,0,0,0.15)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 1px 2px rgba(0,0,0,0.3), 0 1px 3px 1px rgba(0,0,0,0.15)',
        },
      },
    },
  },
});

interface MaterialThemeProviderProps {
  children: React.ReactNode;
}

export default function MaterialThemeProvider({ children }: MaterialThemeProviderProps) {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
}
