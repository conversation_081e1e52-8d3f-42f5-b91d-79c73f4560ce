# Equilibrium

**For your work-life balance**

This is a [Next.js](https://nextjs.org) project for bulk Google Calendar event creation with a mobile-first design approach.

## Features

- 🎨 Material Design 3 inspired UI
- 📱 Mobile-first responsive design
- 🔐 Google OAuth authentication (coming soon)
- 📅 Bulk calendar event creation
- ⚡ Fast and intuitive interface

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load Inter font for a clean, modern look.

## Project Structure

- `/src/app/login` - Landing page with Google sign-in
- `/src/app/dashboard` - Main application interface
- `/src/components` - Reusable UI components
- `/public` - Static assets including the Equilibrium logo

## Technologies Used

- **Next.js 13** - React framework with App Router
- **Material-UI (MUI)** - Material Design components
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **NextAuth.js** - Authentication (planned)

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
